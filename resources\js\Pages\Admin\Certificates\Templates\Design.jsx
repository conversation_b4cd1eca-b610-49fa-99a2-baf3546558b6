import AdminAuthenticatedLayout from '@/Layouts/AdminAuthenticatedLayout';
import { useState, useEffect } from 'react';
import { Rnd } from 'react-rnd';
import { router } from '@inertiajs/react';
import axios from 'axios';
import { toast } from 'sonner';
import { Button } from '@/Components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/Components/ui/dialog";

export default function CertificateDesigner({ editData }) {
  const [elements, setElements] = useState([]);
  const [saving, setSaving] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewHtml, setPreviewHtml] = useState('');
  const [zoom, setZoom] = useState(1);

  // Initialize elements from template data
  useEffect(() => {
    if (editData) {
      const templateElements = [
        {
          id: 'content',
          type: 'content',
          text: editData.content,
          x: 100,
          y: 100,
          width: 400,
          height: 'auto',
        },
      ];

      // Add logo if exists
      if (editData.logo_image) {
        templateElements.push({
          id: 'logo',
          type: 'image',
          src: `/storage/${editData.logo_image}`,
          x: 50,
          y: 50,
          width: 100,
          height: 100,
        });
      }

      // Add signature if exists
      if (editData.signature_image) {
        templateElements.push({
          id: 'signature',
          type: 'image',
          src: `/storage/${editData.signature_image}`,
          x: 400,
          y: 500,
          width: 150,
          height: 60,
        });
      }

      // Add QR code placeholder
      if (editData.qr_code) {
        templateElements.push({
          id: 'qrCode',
          type: 'qr',
          text: '{qrCode}',
          x: 500,
          y: 50,
          width: parseInt(editData.qr_image_size),
          height: parseInt(editData.qr_image_size),
        });
      }

      // Add user photo placeholder if enabled
      if (editData.user_photo_style > 0) {
        templateElements.push({
          id: 'userPhoto',
          type: 'photo',
          text: '{user_image}',
          x: 300,
          y: 50,
          width: parseInt(editData.user_image_size),
          height: parseInt(editData.user_image_size),
          style: editData.user_photo_style,
        });
      }

      // Initialize from saved design if exists
      if (editData.design?.design_content) {
        try {
          const savedDesign = JSON.parse(editData.design.design_content);
          setElements(savedDesign);
        } catch (e) {
          console.error('Error parsing saved design:', e);
          setElements(templateElements);
        }
      } else {
        setElements(templateElements);
      }
    }
  }, [editData]);

  const handleDragStop = (id, d) => {
    setElements((prev) =>
      prev.map((el) => (el.id === id ? { ...el, x: d.x, y: d.y } : el))
    );
  };

  const handleResize = (id, ref, position) => {
    setElements((prev) =>
      prev.map((el) =>
        el.id === id
          ? {
              ...el,
              width: ref.offsetWidth,
              height: ref.offsetHeight,
              x: position.x,
              y: position.y,
            }
          : el
      )
    );
  };

  const saveDesign = async () => {
    if (!editData?.id) return;

    setSaving(true);
    try {
      await axios.post(route('admin.certificate.templates.updateDesign'), {
        template_id: editData.id,
        design_content: JSON.stringify(elements),
      });

      toast.success('Design saved successfully');
    } catch (error) {
      console.error('Error saving design:', error);
      toast.error('Failed to save design');
    } finally {
      setSaving(false);
    }
  };

  const resetDesign = async () => {
    if (!editData?.id) return;

    try {
      await axios.post(route('admin.certificate.templates.designReset', editData.id));
      router.reload();
      toast.success('Design reset successfully');
    } catch (error) {
      console.error('Error resetting design:', error);
      toast.error('Failed to reset design');
    }
  };

  const previewDesign = async () => {
    if (!editData?.id) return;

    try {
      const response = await axios.post(route('admin.certificate.templates.preview'), {
        template: editData.id,
        design_content: generateHtmlFromElements(),
      });

      if (response.data.status === 'success') {
        setPreviewHtml(response.data.data);
        setShowPreview(true);
      }
    } catch (error) {
      console.error('Error previewing design:', error);
      toast.error('Failed to preview design');
    }
  };

  const generateHtmlFromElements = () => {
    return elements
      .map((el) => {
        const style = `position:absolute;left:${el.x}px;top:${el.y}px;` +
                     `width:${el.width}px;height:${el.height}px;`;

        if (el.type === 'image') {
          return `<img src="${el.src}" style="${style}"/>`;
        } else if (el.type === 'qr' || el.type === 'photo') {
          return `<div style="${style}">${el.text}</div>`;
        } else {
          return `<div style="${style}">${el.text}</div>`;
        }
      })
      .join('');
  };

  const handleZoomChange = (factor) => {
    setZoom(factor);
  };

  return (
    <AdminAuthenticatedLayout>
      <div className="min-h-screen bg-gray-100 p-4">
        {/* Toolbar */}
        <div className="fixed top-4 right-4 z-50 bg-white rounded-lg shadow-lg p-4 space-y-4">
          <div className="space-y-2">
            <Button onClick={saveDesign} disabled={saving} className="w-full">
              {saving ? 'Saving...' : 'Save Design'}
            </Button>
            <Button onClick={resetDesign} variant="outline" className="w-full">
              Reset Design
            </Button>
            <Button onClick={previewDesign} variant="secondary" className="w-full">
              Preview
            </Button>
          </div>
          <div className="border-t pt-2">
            <p className="text-sm text-gray-600 mb-2">Zoom</p>
            <div className="flex space-x-2">
              <Button
                onClick={() => handleZoomChange(0.5)}
                variant="outline"
                size="sm"
                className={zoom === 0.5 ? 'bg-gray-100' : ''}
              >
                50%
              </Button>
              <Button
                onClick={() => handleZoomChange(0.75)}
                variant="outline"
                size="sm"
                className={zoom === 0.75 ? 'bg-gray-100' : ''}
              >
                75%
              </Button>
              <Button
                onClick={() => handleZoomChange(1)}
                variant="outline"
                size="sm"
                className={zoom === 1 ? 'bg-gray-100' : ''}
              >
                100%
              </Button>
            </div>
          </div>
        </div>

        {/* Designer Canvas */}
        <div className="flex items-center justify-center min-h-screen pt-16">
          <div 
            className="relative bg-white shadow-2xl"
            style={{
              height: editData ? `${parseFloat(editData.height) * zoom}mm` : `${297 * zoom}mm`,
              width: editData ? `${parseFloat(editData.width) * zoom}mm` : `${210 * zoom}mm`,
              transform: `scale(${zoom})`,
              transformOrigin: 'center center'
            }}
          >
            {editData?.background_image && (
              <img
                src={`/storage/${editData.background_image}`}
                alt="Certificate Background"
                className="absolute top-0 left-0 w-full h-full object-contain z-0"
              />
            )}

            <div className="absolute top-0 left-0 w-full h-full z-10">
              {elements.map((el) => (
                <Rnd
                  key={el.id}
                  default={{
                    x: el.x,
                    y: el.y,
                    width: el.width,
                    height: el.height,
                  }}
                  position={{ x: el.x, y: el.y }}
                  size={{ width: el.width, height: el.height }}
                  onDragStop={(e, d) => handleDragStop(el.id, d)}
                  onResize={(e, direction, ref, delta, position) =>
                    handleResize(el.id, ref, position)
                  }
                  bounds="parent"
                  enableResizing={el.type !== 'qr' && el.type !== 'photo'}
                  scale={zoom}
                >
                  <div
                    className={`
                    border border-gray-400 bg-white/50 rounded shadow cursor-move
                    ${el.type === 'photo' && el.style === 1 ? 'rounded-full overflow-hidden' : ''}
                  `}
                  >
                    {el.type === 'image' ? (
                      <img
                        src={el.src}
                        alt={el.id}
                        className="w-full h-full object-contain"
                      />
                    ) : (
                      <div
                        className="p-2"
                        dangerouslySetInnerHTML={{ __html: el.text }}
                      />
                    )}
                  </div>
                </Rnd>
              ))}
            </div>
          </div>
        </div>

        {/* Preview Modal */}
        <Dialog open={showPreview} onOpenChange={setShowPreview}>
          <DialogContent className="max-w-4xl w-full">
            <DialogHeader>
              <DialogTitle>Certificate Preview</DialogTitle>
            </DialogHeader>
            <div className="mt-4 overflow-auto max-h-[80vh]">
              <div 
                className="bg-white p-4 rounded-lg"
                style={{
                  height: editData ? parseFloat(editData.height) + 'mm' : '297mm',
                  width: editData ? parseFloat(editData.width) + 'mm' : '210mm',
                }}
              >
                <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </AdminAuthenticatedLayout>
  );
}
