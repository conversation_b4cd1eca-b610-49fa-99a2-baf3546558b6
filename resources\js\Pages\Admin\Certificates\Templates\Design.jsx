import AdminAuthenticatedLayout from '@/Layouts/AdminAuthenticatedLayout';
import { useState, useEffect, useRef } from 'react';
import { Rnd } from 'react-rnd';
import { router } from '@inertiajs/react';
import axios from 'axios';
import { toast } from 'sonner';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Separator } from '@/Components/ui/separator';
import {
  Save,
  RotateCcw,
  Eye,
  ZoomIn,
  ZoomOut,
  Move,
  Type,
  Image as ImageIcon,
  QrCode,
  User,
  Download,
  ArrowLeft
} from 'lucide-react';

export default function CertificateDesigner({ editData, auth }) {
  const [elements, setElements] = useState([]);
  const [saving, setSaving] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewHtml, setPreviewHtml] = useState('');
  const [zoom, setZoom] = useState(0.8);
  const [selectedElement, setSelectedElement] = useState(null);
  const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });
  const canvasRef = useRef(null);

  // Initialize elements from template data
  useEffect(() => {
    if (editData) {
      console.log('EditData received:', editData);
      console.log('Design data:', editData.design);
      console.log('Design content:', editData.design?.design_content);

      // Calculate canvas size based on template dimensions
      const width = parseFloat(editData.width?.replace('mm', '') || 210);
      const height = parseFloat(editData.height?.replace('mm', '') || 297);
      setCanvasSize({ width, height });

      const templateElements = [];

      // Add certificate content/body
      if (editData.content) {
        templateElements.push({
          id: 'content',
          type: 'content',
          text: editData.content,
          x: 50,
          y: 150,
          width: width - 100,
          height: 200,
          fontSize: 16,
          fontFamily: 'Arial',
          color: '#000000',
          textAlign: 'center',
        });
      }

      // Add logo if exists
      if (editData.logo_image) {
        templateElements.push({
          id: 'logo',
          type: 'image',
          src: `/storage/${editData.logo_image}`,
          x: 50,
          y: 30,
          width: 80,
          height: 80,
          alt: 'Logo',
        });
      }

      // Add signature if exists
      if (editData.signature_image) {
        templateElements.push({
          id: 'signature',
          type: 'image',
          src: `/storage/${editData.signature_image}`,
          x: width - 200,
          y: height - 120,
          width: 150,
          height: 60,
          alt: 'Signature',
        });
      }

      // Add QR code placeholder
      if (editData.qr_code && editData.qr_image_size) {
        const qrSize = parseInt(editData.qr_image_size) || 100;
        templateElements.push({
          id: 'qrCode',
          type: 'qr',
          text: '{qrCode}',
          x: width - qrSize - 30,
          y: 30,
          width: qrSize,
          height: qrSize,
          backgroundColor: '#f0f0f0',
          border: '2px dashed #ccc',
        });
      }

      // Add user photo placeholder if enabled
      if (editData.user_photo_style > 0 && editData.user_image_size) {
        const photoSize = parseInt(editData.user_image_size) || 100;
        templateElements.push({
          id: 'userPhoto',
          type: 'photo',
          text: '{user_image}',
          x: 50,
          y: height - photoSize - 50,
          width: photoSize,
          height: photoSize,
          style: editData.user_photo_style,
          backgroundColor: '#f0f0f0',
          border: '2px dashed #ccc',
        });
      }

      // Initialize from saved design if exists
      if (editData.design?.design_content) {
        try {
          const savedDesign = JSON.parse(editData.design.design_content);
          if (Array.isArray(savedDesign) && savedDesign.length > 0) {
            setElements(savedDesign);
          } else {
            setElements(templateElements);
          }
        } catch (e) {
          console.error('Error parsing saved design:', e);
          setElements(templateElements);
        }
      } else {
        setElements(templateElements);
      }
    }
  }, [editData]);

  const handleDragStop = (id, d) => {
    setElements((prev) =>
      prev.map((el) => (el.id === id ? { ...el, x: d.x, y: d.y } : el))
    );
  };

  const handleResize = (id, ref, position) => {
    setElements((prev) =>
      prev.map((el) =>
        el.id === id
          ? {
              ...el,
              width: ref.offsetWidth,
              height: ref.offsetHeight,
              x: position.x,
              y: position.y,
            }
          : el
      )
    );
  };

  const saveDesign = async () => {
    if (!editData?.id) return;

    setSaving(true);
    try {


      const response = await axios.post(route('admin.certificate.templates.design.update'), {
        template_id: editData.id,
        design_content: JSON.stringify(elements),
      });

      console.log('Save response:', response.data);
      if (response.data.status === 'success') {
        toast.success('Design saved successfully');
      } else {
        toast.error('Failed to save design: ' + response.data.message);
      }
    } catch (error) {
      console.error('Error saving design:', error);
      console.error('Error details:', error.response?.data);
      console.error('Error status:', error.response?.status);

      let errorMessage = 'Failed to save design';
      if (error.response?.data?.message) {
        errorMessage += ': ' + error.response.data.message;
      } else if (error.response?.data?.errors) {
        const errors = Object.values(error.response.data.errors).flat();
        errorMessage += ': ' + errors.join(', ');
      } else if (error.message) {
        errorMessage += ': ' + error.message;
      }

      toast.error(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const resetDesign = async () => {
    if (!editData?.id) return;

    if (!window.confirm('Are you sure you want to reset the design? This will remove all custom positioning and restore default layout.')) {
      return;
    }

    try {
      const response = await axios.post(route('admin.certificate.templates.design.reset', editData.id));
      if (response.data.status === 'success') {
        // Reload the page to get fresh data
        router.reload();
        toast.success('Design reset successfully');
      }
    } catch (error) {
      console.error('Error resetting design:', error);
      toast.error('Failed to reset design');
    }
  };

  const previewDesign = async () => {
    if (!editData?.id) return;

    try {
      const response = await axios.post(route('admin.certificate.templates.preview'), {
        template: editData.id,
        design_content: generateHtmlFromElements(),
      });

      if (response.data.status === 'success') {
        setPreviewHtml(response.data.data);
        setShowPreview(true);
      }
    } catch (error) {
      console.error('Error previewing design:', error);
      toast.error('Failed to preview design');
    }
  };

  const generateHtmlFromElements = () => {
    return elements
      .map((el) => {
        const style = `position:absolute;left:${el.x}px;top:${el.y}px;` +
                     `width:${el.width}px;height:${el.height}px;`;

        if (el.type === 'image') {
          return `<img src="${el.src}" style="${style}"/>`;
        } else if (el.type === 'qr' || el.type === 'photo') {
          return `<div style="${style}">${el.text}</div>`;
        } else {
          return `<div style="${style}">${el.text}</div>`;
        }
      })
      .join('');
  };

  const handleZoomChange = (factor) => {
    setZoom(factor);
  };

  return (
    <AdminAuthenticatedLayout user={auth?.user}>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.visit(route('admin.certificate.templates.index'))}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Templates</span>
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Certificate Designer
                </h1>
                <p className="text-sm text-gray-500">
                  {editData?.name || 'Untitled Template'}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <Button
                onClick={resetDesign}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2"
              >
                <RotateCcw className="w-4 h-4" />
                <span>Reset</span>
              </Button>
              <Button
                onClick={previewDesign}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2"
              >
                <Eye className="w-4 h-4" />
                <span>Preview</span>
              </Button>
              <Button
                onClick={saveDesign}
                disabled={saving}
                size="sm"
                className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700"
              >
                <Save className="w-4 h-4" />
                <span>{saving ? 'Saving...' : 'Update Design'}</span>
              </Button>
            </div>
          </div>
        </div>

        <div className="flex h-[calc(100vh-80px)]">
          {/* Left Sidebar - Tools */}
          <div className="w-64 bg-white border-r border-gray-200 p-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Design Tools</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Zoom Controls */}
                <div>
                  <label className="text-xs font-medium text-gray-700 mb-2 block">
                    Zoom Level
                  </label>
                  <div className="grid grid-cols-3 gap-1">
                    {[0.5, 0.75, 1].map((level) => (
                      <Button
                        key={level}
                        onClick={() => handleZoomChange(level)}
                        variant={zoom === level ? "default" : "outline"}
                        size="sm"
                        className="text-xs"
                      >
                        {Math.round(level * 100)}%
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Element Info */}
                <div>
                  <label className="text-xs font-medium text-gray-700 mb-2 block">
                    Elements
                  </label>
                  <div className="space-y-1 text-xs">
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span>Total Elements</span>
                      <Badge variant="secondary">{elements.length}</Badge>
                    </div>
                  </div>
                </div>

                {/* Template Info */}
                <div>
                  <label className="text-xs font-medium text-gray-700 mb-2 block">
                    Canvas Size
                  </label>
                  <div className="text-xs text-gray-600">
                    {canvasSize.width} × {canvasSize.height} mm
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Canvas Area */}
          <div className="flex-1 bg-gray-100 p-8 overflow-auto">
            <div className="flex items-center justify-center min-h-full">
              <div
                ref={canvasRef}
                className="relative bg-white shadow-2xl border border-gray-300 overflow-hidden"
                style={{
                  width: `${canvasSize.width * zoom}mm`,
                  height: `${canvasSize.height * zoom}mm`,
                  minWidth: '400px',
                  minHeight: '300px',
                }}
              >
                {/* Certificate boundary indicator */}
                <div
                  className="absolute inset-0 pointer-events-none border-2 border-dashed border-blue-400 opacity-20"
                  style={{ zIndex: 5 }}
                />

                {/* Background Image */}
                {editData?.background_image && (
                  <img
                    src={`/storage/${editData.background_image}`}
                    alt="Certificate Background"
                    className="absolute top-0 left-0 w-full h-full object-cover z-0"
                    style={{ pointerEvents: 'none' }}
                  />
                )}

                {/* Draggable Elements */}
                <div className="absolute top-0 left-0 w-full h-full z-10">
                  {elements.map((el) => (
                    <Rnd
                      key={el.id}
                      default={{
                        x: el.x * zoom,
                        y: el.y * zoom,
                        width: el.width * zoom,
                        height: el.height * zoom,
                      }}
                      position={{ x: el.x * zoom, y: el.y * zoom }}
                      size={{ width: el.width * zoom, height: el.height * zoom }}
                      onDragStop={(e, d) => {
                        // Ensure element stays within canvas bounds
                        const maxX = (canvasSize.width * zoom) - (el.width * zoom);
                        const maxY = (canvasSize.height * zoom) - (el.height * zoom);
                        const constrainedX = Math.max(0, Math.min(d.x, maxX));
                        const constrainedY = Math.max(0, Math.min(d.y, maxY));
                        handleDragStop(el.id, { x: constrainedX / zoom, y: constrainedY / zoom });
                      }}
                      onResize={(e, direction, ref, delta, position) => {
                        // Ensure resized element stays within canvas bounds
                        const newWidth = parseInt(ref.style.width);
                        const newHeight = parseInt(ref.style.height);
                        const maxX = (canvasSize.width * zoom) - newWidth;
                        const maxY = (canvasSize.height * zoom) - newHeight;
                        const constrainedX = Math.max(0, Math.min(position.x, maxX));
                        const constrainedY = Math.max(0, Math.min(position.y, maxY));
                        handleResize(el.id, ref, { x: constrainedX / zoom, y: constrainedY / zoom });
                      }}
                      bounds="parent"
                      enableResizing={{
                        top: el.type !== 'qr' && el.type !== 'photo',
                        right: el.type !== 'qr' && el.type !== 'photo',
                        bottom: el.type !== 'qr' && el.type !== 'photo',
                        left: el.type !== 'qr' && el.type !== 'photo',
                        topRight: el.type !== 'qr' && el.type !== 'photo',
                        bottomRight: el.type !== 'qr' && el.type !== 'photo',
                        bottomLeft: el.type !== 'qr' && el.type !== 'photo',
                        topLeft: el.type !== 'qr' && el.type !== 'photo',
                      }}
                      onClick={() => setSelectedElement(el.id)}
                    >
                      <div
                        className={`
                          w-full h-full border-2 transition-all duration-200
                          ${selectedElement === el.id
                            ? 'border-blue-500 shadow-lg'
                            : 'border-gray-300 hover:border-gray-400'
                          }
                          ${el.type === 'photo' && el.style === 1 ? 'rounded-full overflow-hidden' : 'rounded'}
                          ${el.type === 'qr' || el.type === 'photo' ? 'bg-gray-50' : 'bg-white/90'}
                          cursor-move
                        `}
                        style={{
                          backgroundColor: el.backgroundColor || (el.type === 'content' ? 'transparent' : '#f9f9f9'),
                          border: el.border || undefined,
                        }}
                      >
                        {el.type === 'image' ? (
                          <img
                            src={el.src}
                            alt={el.alt || el.id}
                            className="w-full h-full object-contain"
                            draggable={false}
                          />
                        ) : el.type === 'content' ? (
                          <div
                            className="w-full h-full p-2 overflow-hidden"
                            style={{
                              fontSize: `${(el.fontSize || 16) * zoom}px`,
                              fontFamily: el.fontFamily || 'Arial',
                              color: el.color || '#000000',
                              textAlign: el.textAlign || 'center',
                            }}
                            dangerouslySetInnerHTML={{ __html: el.text }}
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-gray-500 text-sm">
                            {el.type === 'qr' && <QrCode className="w-6 h-6 mb-1" />}
                            {el.type === 'photo' && <User className="w-6 h-6 mb-1" />}
                            <div className="text-center">
                              {el.text}
                            </div>
                          </div>
                        )}
                      </div>
                    </Rnd>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Preview Overlay */}
        {showPreview && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-2xl w-full h-full max-w-7xl max-h-[95vh] flex flex-col">
              <div className="flex-shrink-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">Certificate Preview</h2>
                <div className="flex items-center space-x-2">
                  <Button
                    onClick={() => {
                      // Generate and download preview
                      const element = document.createElement('a');
                      const file = new Blob([previewHtml], { type: 'text/html' });
                      element.href = URL.createObjectURL(file);
                      element.download = `${editData?.name || 'certificate'}-preview.html`;
                      document.body.appendChild(element);
                      element.click();
                      document.body.removeChild(element);
                    }}
                    variant="outline"
                    size="sm"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                  <Button
                    onClick={() => setShowPreview(false)}
                    variant="outline"
                    size="sm"
                  >
                    Close
                  </Button>
                </div>
              </div>
              <div className="flex-1 flex items-center justify-center p-6 overflow-hidden">
                <div
                  className="bg-white border border-gray-300 shadow-lg relative"
                  style={{
                    width: `${canvasSize.width}mm`,
                    height: `${canvasSize.height}mm`,
                    maxWidth: '90%',
                    maxHeight: '90%',
                    transform: `scale(${Math.min(
                      (window.innerWidth * 0.8) / (canvasSize.width * 3.78),
                      (window.innerHeight * 0.7) / (canvasSize.height * 3.78),
                      1
                    )})`,
                    transformOrigin: 'center center',
                    overflow: 'hidden',
                  }}
                >
                  <div
                    dangerouslySetInnerHTML={{ __html: previewHtml }}
                    className="w-full h-full overflow-hidden"
                    style={{
                      position: 'relative',
                      width: `${canvasSize.width}mm`,
                      height: `${canvasSize.height}mm`,
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminAuthenticatedLayout>
  );
}
